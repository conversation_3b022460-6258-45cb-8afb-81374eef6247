/**
 * Voice Bot UI Module
 * Uživatelské rozhraní pro hlasové ovládání
 */

class VoiceBotUI {
    constructor() {
        this.container = null;
        this.toggleButton = null;
        this.indicator = null;
        this.helpPanel = null;
        this.isHelpVisible = false;
        
        this.init();
    }

    /**
     * Inicializace UI komponent
     */
    init() {
        this.createContainer();
        this.createToggleButton();
        this.createIndicator();
        this.createHelpPanel();
        this.attachEventListeners();
        
        console.log('Voice Bot UI: Inicializace dokončena');
    }

    /**
     * Vytvoření hlavního kontejneru
     */
    createContainer() {
        this.container = document.createElement('div');
        this.container.id = 'voiceBotContainer';
        this.container.className = 'voice-bot-container loading';
        
        // Přidání do body
        document.body.appendChild(this.container);
        
        // Odstranění loading třídy po animaci
        setTimeout(() => {
            this.container.classList.remove('loading');
        }, 500);
    }

    /**
     * Vytvoření tlačítka pro zapnutí/vypnutí
     */
    createToggleButton() {
        this.toggleButton = document.createElement('button');
        this.toggleButton.id = 'voiceBotToggle';
        this.toggleButton.className = 'voice-bot-toggle';
        this.toggleButton.title = 'Zapnout hlasové ovládání';
        this.toggleButton.innerHTML = `
            <div class="microphone-icon"></div>
        `;
        
        this.container.appendChild(this.toggleButton);
    }

    /**
     * Vytvoření indikátoru stavu
     */
    createIndicator() {
        this.indicator = document.createElement('div');
        this.indicator.id = 'voiceBotIndicator';
        this.indicator.className = 'voice-bot-indicator';
        this.indicator.innerHTML = `
            <div class="voice-status-icon"></div>
            <div class="voice-status-text">Neaktivní</div>
        `;
        
        this.container.appendChild(this.indicator);
    }

    /**
     * Vytvoření panelu nápovědy
     */
    createHelpPanel() {
        this.helpPanel = document.createElement('div');
        this.helpPanel.id = 'voiceCommandsHelp';
        this.helpPanel.className = 'voice-commands-help';
        this.helpPanel.innerHTML = `
            <h4>Hlasové příkazy:</h4>
            <ul class="voice-commands-list">
                <li><strong>"přidat bod"</strong> - přidá nový bod na mapu</li>
                <li><strong>"vypočítat trasu"</strong> - vypočítá trasu mezi body</li>
                <li><strong>"vymazat mapu"</strong> - vymaže všechny body</li>
                <li><strong>"fullscreen"</strong> - přepne do režimu celé obrazovky</li>
                <li><strong>"glóbus"</strong> - přepne do 3D režimu</li>
                <li><strong>"alexa"</strong> - zobrazí klub Alexa</li>
                <li><strong>"otevírací doba"</strong> - zobrazí otevírací dobu</li>
                <li><strong>"stop"</strong> - zastaví naslouchání</li>
            </ul>
        `;
        
        this.container.appendChild(this.helpPanel);
    }

    /**
     * Připojení event listenerů
     */
    attachEventListeners() {
        // Kliknutí na toggle tlačítko
        this.toggleButton.addEventListener('click', () => {
            if (window.VoiceBot) {
                window.VoiceBot.toggle();
            }
        });

        // Dlouhé stisknutí pro zobrazení nápovědy
        let longPressTimer;
        
        this.toggleButton.addEventListener('mousedown', () => {
            longPressTimer = setTimeout(() => {
                this.toggleHelp();
            }, 1000);
        });

        this.toggleButton.addEventListener('mouseup', () => {
            clearTimeout(longPressTimer);
        });

        this.toggleButton.addEventListener('mouseleave', () => {
            clearTimeout(longPressTimer);
        });

        // Touch eventy pro mobilní zařízení
        this.toggleButton.addEventListener('touchstart', (e) => {
            e.preventDefault();
            longPressTimer = setTimeout(() => {
                this.toggleHelp();
            }, 1000);
        });

        this.toggleButton.addEventListener('touchend', (e) => {
            e.preventDefault();
            clearTimeout(longPressTimer);
            
            // Krátké stisknutí - toggle voice bot
            if (window.VoiceBot) {
                window.VoiceBot.toggle();
            }
        });

        // Klávesové zkratky
        document.addEventListener('keydown', (e) => {
            // Ctrl + Shift + V pro toggle voice bot
            if (e.ctrlKey && e.shiftKey && e.key === 'V') {
                e.preventDefault();
                if (window.VoiceBot) {
                    window.VoiceBot.toggle();
                }
            }
            
            // Ctrl + Shift + H pro nápovědu
            if (e.ctrlKey && e.shiftKey && e.key === 'H') {
                e.preventDefault();
                this.toggleHelp();
            }
        });

        // Kliknutí mimo nápovědu ji zavře
        document.addEventListener('click', (e) => {
            if (this.isHelpVisible && 
                !this.helpPanel.contains(e.target) && 
                !this.toggleButton.contains(e.target)) {
                this.hideHelp();
            }
        });
    }

    /**
     * Zobrazení/skrytí nápovědy
     */
    toggleHelp() {
        if (this.isHelpVisible) {
            this.hideHelp();
        } else {
            this.showHelp();
        }
    }

    /**
     * Zobrazení nápovědy
     */
    showHelp() {
        this.helpPanel.classList.add('visible');
        this.isHelpVisible = true;
        
        // Automatické skrytí po 10 sekundách
        setTimeout(() => {
            if (this.isHelpVisible) {
                this.hideHelp();
            }
        }, 10000);
    }

    /**
     * Skrytí nápovědy
     */
    hideHelp() {
        this.helpPanel.classList.remove('visible');
        this.isHelpVisible = false;
    }

    /**
     * Aktualizace stavu UI
     */
    updateStatus(status) {
        if (!status) return;

        // Aktualizace tlačítka
        this.toggleButton.classList.toggle('active', status.isEnabled);
        this.toggleButton.title = status.isEnabled ? 
            'Vypnout hlasové ovládání (Ctrl+Shift+V)' : 
            'Zapnout hlasové ovládání (Ctrl+Shift+V)';

        // Aktualizace indikátoru
        this.indicator.classList.toggle('enabled', status.isEnabled);
        this.indicator.classList.toggle('listening', status.isListening);

        // Aktualizace textu stavu
        const statusText = this.indicator.querySelector('.voice-status-text');
        if (statusText) {
            if (status.isListening) {
                statusText.textContent = 'Naslouchám...';
            } else if (status.isEnabled) {
                statusText.textContent = 'Připraven';
            } else {
                statusText.textContent = 'Neaktivní';
            }
        }
    }

    /**
     * Zobrazení chybového stavu
     */
    showError(message) {
        this.indicator.classList.add('error');
        
        const statusText = this.indicator.querySelector('.voice-status-text');
        if (statusText) {
            statusText.textContent = message;
        }

        // Odstranění chybového stavu po 5 sekundách
        setTimeout(() => {
            this.indicator.classList.remove('error');
            if (window.VoiceBot) {
                this.updateStatus(window.VoiceBot.getStatus());
            }
        }, 5000);
    }

    /**
     * Zobrazení zprávy o rozpoznaném příkazu
     */
    showRecognizedCommand(command) {
        const statusText = this.indicator.querySelector('.voice-status-text');
        if (statusText) {
            statusText.textContent = `Rozpoznáno: "${command}"`;
            
            // Resetování po 3 sekundách
            setTimeout(() => {
                if (window.VoiceBot) {
                    this.updateStatus(window.VoiceBot.getStatus());
                }
            }, 3000);
        }
    }

    /**
     * Zobrazení průběžného přepisu
     */
    showInterimResult(text) {
        const statusText = this.indicator.querySelector('.voice-status-text');
        if (statusText) {
            statusText.textContent = `Naslouchám: "${text}"`;
        }
    }

    /**
     * Přidání vlastního příkazu do nápovědy
     */
    addCommandToHelp(command, description) {
        const commandsList = this.helpPanel.querySelector('.voice-commands-list');
        if (commandsList) {
            const listItem = document.createElement('li');
            listItem.innerHTML = `<strong>"${command}"</strong> - ${description}`;
            commandsList.appendChild(listItem);
        }
    }

    /**
     * Odstranění UI
     */
    destroy() {
        if (this.container && this.container.parentNode) {
            this.container.parentNode.removeChild(this.container);
        }
        
        // Odstranění event listenerů
        document.removeEventListener('keydown', this.handleKeydown);
        document.removeEventListener('click', this.handleDocumentClick);
        
        console.log('Voice Bot UI: Zničeno');
    }

    /**
     * Skrytí/zobrazení UI
     */
    setVisible(visible) {
        if (this.container) {
            this.container.style.display = visible ? 'flex' : 'none';
        }
    }

    /**
     * Nastavení pozice UI
     */
    setPosition(position) {
        if (!this.container) return;

        // Resetování pozice
        this.container.style.top = '';
        this.container.style.right = '';
        this.container.style.bottom = '';
        this.container.style.left = '';

        switch (position) {
            case 'top-right':
                this.container.style.top = '20px';
                this.container.style.right = '20px';
                break;
            case 'top-left':
                this.container.style.top = '20px';
                this.container.style.left = '20px';
                break;
            case 'bottom-right':
                this.container.style.bottom = '20px';
                this.container.style.right = '20px';
                break;
            case 'bottom-left':
                this.container.style.bottom = '20px';
                this.container.style.left = '20px';
                break;
            default:
                this.container.style.top = '20px';
                this.container.style.right = '20px';
        }
    }
}

// Globální instance UI
let voiceBotUI = null;

// Inicializace UI po načtení stránky
document.addEventListener('DOMContentLoaded', function() {
    // Čekáme na načtení CSS
    setTimeout(() => {
        voiceBotUI = new VoiceBotUI();
        
        // Přidání do globálního objektu window
        window.VoiceBotUI = voiceBotUI;
        
        console.log('Voice Bot UI: Globální instance vytvořena');
    }, 500);
});

// Export pro použití v jiných modulech
if (typeof module !== 'undefined' && module.exports) {
    module.exports = VoiceBotUI;
}
