/**
 * Voice <PERSON>t CSS Styles
 * <PERSON><PERSON>y pro hlasové ovl<PERSON><PERSON><PERSON><PERSON> aplikace
 */

/* <PERSON><PERSON><PERSON><PERSON> pro voice bot ov<PERSON><PERSON><PERSON><PERSON><PERSON> */
.voice-bot-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    display: flex;
    flex-direction: column;
    gap: 10px;
    align-items: flex-end;
}

/* Tlačítko pro zapnutí/vypnutí voice bota */
.voice-bot-toggle {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 50%;
    width: 60px;
    height: 60px;
    color: white;
    font-size: 24px;
    cursor: pointer;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.voice-bot-toggle:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.voice-bot-toggle.active {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
    animation: pulse 2s infinite;
}

.voice-bot-toggle.active:hover {
    box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
}

/* Animace pulzování pro aktivní stav */
@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

/* Indikátor stavu voice bota */
.voice-bot-indicator {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 25px;
    padding: 12px 20px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    gap: 12px;
    min-width: 200px;
    opacity: 0;
    transform: translateX(20px);
    transition: all 0.3s ease;
    pointer-events: none;
}

.voice-bot-indicator.enabled {
    opacity: 1;
    transform: translateX(0);
    pointer-events: auto;
}

.voice-bot-indicator.listening {
    background: rgba(255, 107, 107, 0.1);
    border-color: rgba(255, 107, 107, 0.3);
}

/* Ikona stavu */
.voice-status-icon {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #ccc;
    transition: all 0.3s ease;
    position: relative;
}

.voice-bot-indicator.enabled .voice-status-icon {
    background: #4CAF50;
}

.voice-bot-indicator.listening .voice-status-icon {
    background: #ff6b6b;
    animation: listening-pulse 1.5s infinite;
}

@keyframes listening-pulse {
    0%, 100% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.2);
        opacity: 0.7;
    }
}

/* Text stavu */
.voice-status-text {
    font-size: 14px;
    font-weight: 500;
    color: #333;
    white-space: nowrap;
}

.voice-bot-indicator.listening .voice-status-text {
    color: #ff6b6b;
}

/* Mikrofon ikona */
.voice-bot-toggle .microphone-icon {
    position: relative;
    width: 24px;
    height: 24px;
}

.voice-bot-toggle .microphone-icon::before {
    content: '🎤';
    font-size: 24px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.voice-bot-toggle.active .microphone-icon::before {
    content: '🔴';
}

/* Vlnový efekt při naslouchání */
.voice-bot-toggle.active::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 100%;
    height: 100%;
    border: 2px solid rgba(255, 255, 255, 0.6);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    animation: wave 2s infinite;
}

@keyframes wave {
    0% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) scale(1.5);
        opacity: 0;
    }
}

/* Příkazy nápovědy */
.voice-commands-help {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 15px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    max-width: 300px;
    opacity: 0;
    transform: translateX(20px);
    transition: all 0.3s ease;
    pointer-events: none;
}

.voice-commands-help.visible {
    opacity: 1;
    transform: translateX(0);
    pointer-events: auto;
}

.voice-commands-help h4 {
    margin: 0 0 10px 0;
    font-size: 16px;
    color: #333;
    font-weight: 600;
}

.voice-commands-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.voice-commands-list li {
    padding: 5px 0;
    font-size: 13px;
    color: #666;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.voice-commands-list li:last-child {
    border-bottom: none;
}

.voice-commands-list li strong {
    color: #333;
}

/* Responsivní design */
@media (max-width: 768px) {
    .voice-bot-container {
        top: 10px;
        right: 10px;
    }
    
    .voice-bot-toggle {
        width: 50px;
        height: 50px;
        font-size: 20px;
    }
    
    .voice-bot-indicator {
        min-width: 150px;
        padding: 10px 15px;
    }
    
    .voice-status-text {
        font-size: 12px;
    }
    
    .voice-commands-help {
        max-width: 250px;
        padding: 12px;
    }
}

/* Tmavý režim */
@media (prefers-color-scheme: dark) {
    .voice-bot-indicator {
        background: rgba(30, 30, 30, 0.95);
        border-color: rgba(255, 255, 255, 0.1);
    }
    
    .voice-status-text {
        color: #fff;
    }
    
    .voice-commands-help {
        background: rgba(30, 30, 30, 0.95);
        border-color: rgba(255, 255, 255, 0.1);
    }
    
    .voice-commands-help h4 {
        color: #fff;
    }
    
    .voice-commands-list li {
        color: #ccc;
        border-bottom-color: rgba(255, 255, 255, 0.1);
    }
    
    .voice-commands-list li strong {
        color: #fff;
    }
}

/* Animace při načítání */
.voice-bot-container.loading {
    opacity: 0;
    animation: fadeInUp 0.5s ease forwards;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Chybový stav */
.voice-bot-indicator.error {
    background: rgba(255, 107, 107, 0.1);
    border-color: rgba(255, 107, 107, 0.3);
}

.voice-bot-indicator.error .voice-status-icon {
    background: #ff6b6b;
}

.voice-bot-indicator.error .voice-status-text {
    color: #ff6b6b;
}

/* Tooltip pro tlačítko */
.voice-bot-toggle::before {
    content: attr(title);
    position: absolute;
    bottom: -35px;
    right: 0;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 5px 10px;
    border-radius: 5px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s ease;
    z-index: 1001;
}

.voice-bot-toggle:hover::before {
    opacity: 1;
}
