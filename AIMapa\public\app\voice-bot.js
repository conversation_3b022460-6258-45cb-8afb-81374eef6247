/**
 * Voice Bot Module pro AIMapa
 * Verze 1.0.0
 * 
 * Modul pro hlasové ovládání aplikace pomocí Web Speech API
 */

class VoiceBot {
    constructor() {
        this.recognition = null;
        this.synthesis = window.speechSynthesis;
        this.isListening = false;
        this.isEnabled = false;
        this.currentLanguage = 'cs-CZ';
        this.voiceCommands = new Map();
        
        // Inicializace
        this.init();
        this.setupVoiceCommands();
    }

    /**
     * Inicializace voice bot systému
     */
    init() {
        // Kontrola podpory Web Speech API
        if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
            console.error('Web Speech API není podporováno v tomto prohlížeči');
            this.showUnsupportedMessage();
            return;
        }

        // Inicializace rozpoznávání řeči
        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
        this.recognition = new SpeechRecognition();
        
        this.recognition.continuous = true;
        this.recognition.interimResults = true;
        this.recognition.lang = this.currentLanguage;
        this.recognition.maxAlternatives = 1;

        // Event listenery pro rozpoznávání řeči
        this.recognition.onstart = () => {
            console.log('Voice Bot: Rozpoznávání řeči spuštěno');
            this.isListening = true;
            this.updateUI();
        };

        this.recognition.onend = () => {
            console.log('Voice Bot: Rozpoznávání řeči ukončeno');
            this.isListening = false;
            this.updateUI();
            
            // Automatické restartování pokud je voice bot aktivní
            if (this.isEnabled) {
                setTimeout(() => {
                    this.startListening();
                }, 1000);
            }
        };

        this.recognition.onresult = (event) => {
            this.handleSpeechResult(event);
        };

        this.recognition.onerror = (event) => {
            console.error('Voice Bot: Chyba rozpoznávání řeči:', event.error);
            this.handleSpeechError(event.error);
        };

        console.log('Voice Bot: Inicializace dokončena');
    }

    /**
     * Nastavení hlasových příkazů
     */
    setupVoiceCommands() {
        // Základní příkazy pro mapu
        this.voiceCommands.set('přidat bod', () => this.executeMapCommand('addActivity'));
        this.voiceCommands.set('přidat aktivitu', () => this.executeMapCommand('addActivity'));
        this.voiceCommands.set('přidat marker', () => this.executeMapCommand('addActivity'));
        this.voiceCommands.set('nový bod', () => this.executeMapCommand('addActivity'));
        this.voiceCommands.set('vypočítat trasu', () => this.executeMapCommand('calculateRoute'));
        this.voiceCommands.set('spočítat trasu', () => this.executeMapCommand('calculateRoute'));
        this.voiceCommands.set('najít cestu', () => this.executeMapCommand('calculateRoute'));
        this.voiceCommands.set('vymazat mapu', () => this.executeMapCommand('clearMap'));
        this.voiceCommands.set('smazat mapu', () => this.executeMapCommand('clearMap'));
        this.voiceCommands.set('vyčistit mapu', () => this.executeMapCommand('clearMap'));
        this.voiceCommands.set('vytisknout mapu', () => this.executeMapCommand('printMap'));
        this.voiceCommands.set('tisknout mapu', () => this.executeMapCommand('printMap'));

        // Příkazy pro režimy
        this.voiceCommands.set('fullscreen', () => this.executeMapCommand('fullscreen'));
        this.voiceCommands.set('celá obrazovka', () => this.executeMapCommand('fullscreen'));
        this.voiceCommands.set('plná obrazovka', () => this.executeMapCommand('fullscreen'));
        this.voiceCommands.set('glóbus', () => this.executeMapCommand('globe'));
        this.voiceCommands.set('režim glóbus', () => this.executeMapCommand('globe'));
        this.voiceCommands.set('3d režim', () => this.executeMapCommand('globe'));
        this.voiceCommands.set('trojrozměrný režim', () => this.executeMapCommand('globe'));

        // Příkazy pro navigaci na mapě
        this.voiceCommands.set('přiblížit', () => this.executeMapNavigation('zoomIn'));
        this.voiceCommands.set('oddálit', () => this.executeMapNavigation('zoomOut'));
        this.voiceCommands.set('zoom in', () => this.executeMapNavigation('zoomIn'));
        this.voiceCommands.set('zoom out', () => this.executeMapNavigation('zoomOut'));
        this.voiceCommands.set('vycentrovat mapu', () => this.executeMapNavigation('center'));
        this.voiceCommands.set('střed mapy', () => this.executeMapNavigation('center'));

        // Příkazy pro AI asistenta
        this.voiceCommands.set('alexa', () => this.sendToAI('alexa'));
        this.voiceCommands.set('otevírací doba', () => this.sendToAI('otevírací doba'));
        this.voiceCommands.set('oteviraci doba', () => this.sendToAI('otevírací doba'));
        this.voiceCommands.set('kdy je otevřeno', () => this.sendToAI('otevírací doba'));

        // Příkazy pro nastavení
        this.voiceCommands.set('nastavení', () => this.executeMapCommand('settings'));
        this.voiceCommands.set('otevřít nastavení', () => this.executeMapCommand('settings'));
        this.voiceCommands.set('možnosti', () => this.executeMapCommand('settings'));

        // Příkazy pro ovládání voice bota
        this.voiceCommands.set('stop', () => this.stopListening());
        this.voiceCommands.set('zastavit', () => this.stopListening());
        this.voiceCommands.set('pauza', () => this.stopListening());
        this.voiceCommands.set('vypnout hlasové ovládání', () => this.disable());
        this.voiceCommands.set('zapnout hlasové ovládání', () => this.enable());
        this.voiceCommands.set('hlasové ovládání vypnout', () => this.disable());
        this.voiceCommands.set('hlasové ovládání zapnout', () => this.enable());

        // Příkazy pro nápovědu
        this.voiceCommands.set('nápověda', () => this.showVoiceHelp());
        this.voiceCommands.set('pomoc', () => this.showVoiceHelp());
        this.voiceCommands.set('help', () => this.showVoiceHelp());
        this.voiceCommands.set('co můžu říct', () => this.showVoiceHelp());
        this.voiceCommands.set('jaké příkazy', () => this.showVoiceHelp());

        console.log('Voice Bot: Hlasové příkazy nastaveny');
    }

    /**
     * Zpracování výsledku rozpoznávání řeči
     */
    handleSpeechResult(event) {
        let finalTranscript = '';
        let interimTranscript = '';

        for (let i = event.resultIndex; i < event.results.length; i++) {
            const transcript = event.results[i][0].transcript;
            
            if (event.results[i].isFinal) {
                finalTranscript += transcript;
            } else {
                interimTranscript += transcript;
            }
        }

        // Zobrazení průběžného přepisu
        if (interimTranscript) {
            this.showInterimResult(interimTranscript);
        }

        // Zpracování finálního přepisu
        if (finalTranscript) {
            this.processVoiceCommand(finalTranscript.trim().toLowerCase());
        }
    }

    /**
     * Zpracování hlasového příkazu
     */
    processVoiceCommand(command) {
        console.log('Voice Bot: Zpracovávám příkaz:', command);
        
        // Zobrazení rozpoznaného příkazu v UI
        this.showRecognizedCommand(command);
        
        // Hledání přesné shody
        if (this.voiceCommands.has(command)) {
            this.voiceCommands.get(command)();
            return;
        }
        
        // Hledání částečné shody
        for (const [voiceCommand, action] of this.voiceCommands) {
            if (command.includes(voiceCommand)) {
                action();
                return;
            }
        }
        
        // Pokud nebyl nalezen specifický příkaz, pošleme do AI asistenta
        this.sendToAI(command);
    }

    /**
     * Provedení příkazu pro mapu
     */
    executeMapCommand(command) {
        let button = null;
        let message = '';

        switch (command) {
            case 'addActivity':
                button = document.getElementById('addActivity');
                message = 'Režim přidávání bodů byl aktivován';
                break;
            case 'calculateRoute':
                button = document.getElementById('calculateRoute');
                message = 'Vypočítávám trasu mezi body';
                break;
            case 'clearMap':
                button = document.getElementById('clearMap');
                message = 'Mapa byla vymazána';
                break;
            case 'printMap':
                button = document.getElementById('printMap');
                message = 'Mapa se připravuje k tisku';
                break;
            case 'fullscreen':
                button = document.getElementById('fullscreenButton');
                message = 'Přepínám do režimu celé obrazovky';
                break;
            case 'globe':
                button = document.getElementById('toggleGlobeMode');
                message = 'Přepínám do 3D režimu glóbusu';
                break;
            case 'settings':
                button = document.getElementById('settingsButton');
                message = 'Otevírám nastavení aplikace';
                break;
            default:
                this.speak('Neznámý příkaz pro mapu');
                return;
        }

        if (button) {
            button.click();
            this.speak(message);
        } else {
            this.speak('Tlačítko pro tento příkaz nebylo nalezeno');
        }
    }

    /**
     * Provedení navigačního příkazu na mapě
     */
    executeMapNavigation(command) {
        if (!window.map) {
            this.speak('Mapa není dostupná');
            return;
        }

        switch (command) {
            case 'zoomIn':
                window.map.zoomIn();
                this.speak('Přibližuji mapu');
                break;
            case 'zoomOut':
                window.map.zoomOut();
                this.speak('Oddaluji mapu');
                break;
            case 'center':
                // Vycentrování na Hodonín (výchozí pozice)
                window.map.setView([48.8553, 17.1225], 13);
                this.speak('Mapa byla vycentrována');
                break;
            default:
                this.speak('Neznámý navigační příkaz');
        }
    }

    /**
     * Zobrazení hlasové nápovědy
     */
    showVoiceHelp() {
        const commands = [
            'Přidat bod - přidá nový bod na mapu',
            'Vypočítat trasu - vypočítá cestu mezi body',
            'Vymazat mapu - odstraní všechny body',
            'Fullscreen - přepne do režimu celé obrazovky',
            'Glóbus - přepne do 3D režimu',
            'Přiblížit nebo oddálit - změní zoom mapy',
            'Nastavení - otevře možnosti aplikace',
            'Stop - zastaví naslouchání'
        ];

        let helpText = 'Dostupné hlasové příkazy: ';
        helpText += commands.join('. ');

        this.speak(helpText);

        // Zobrazení nápovědy také v UI
        if (window.VoiceBotUI) {
            window.VoiceBotUI.showHelp();
        }
    }

    /**
     * Odeslání zprávy do AI asistenta
     */
    sendToAI(message) {
        if (typeof processMessage === 'function') {
            processMessage(message);
            this.speak('Zpráva byla odeslána do AI asistenta');
        } else {
            console.error('Voice Bot: Funkce processMessage není dostupná');
            this.speak('Chyba při komunikaci s AI asistentem');
        }
    }

    /**
     * Syntéza řeči - převod textu na řeč
     */
    speak(text, options = {}) {
        if (!this.synthesis) {
            console.error('Voice Bot: Speech Synthesis není podporováno');
            return;
        }

        // Zastavení předchozí řeči
        this.synthesis.cancel();

        const utterance = new SpeechSynthesisUtterance(text);
        utterance.lang = options.lang || this.currentLanguage;
        utterance.rate = options.rate || 1;
        utterance.pitch = options.pitch || 1;
        utterance.volume = options.volume || 1;

        // Hledání českého hlasu
        const voices = this.synthesis.getVoices();
        const czechVoice = voices.find(voice => voice.lang.startsWith('cs'));
        if (czechVoice) {
            utterance.voice = czechVoice;
        }

        utterance.onstart = () => {
            console.log('Voice Bot: Začínám mluvit:', text);
        };

        utterance.onend = () => {
            console.log('Voice Bot: Dokončeno mluvení');
        };

        utterance.onerror = (event) => {
            console.error('Voice Bot: Chyba při mluvení:', event.error);
        };

        this.synthesis.speak(utterance);
    }

    /**
     * Spuštění naslouchání
     */
    startListening() {
        if (!this.recognition) {
            console.error('Voice Bot: Rozpoznávání řeči není inicializováno');
            return;
        }

        if (this.isListening) {
            console.log('Voice Bot: Již naslouchám');
            return;
        }

        try {
            this.recognition.start();
            console.log('Voice Bot: Spouštím naslouchání');
        } catch (error) {
            console.error('Voice Bot: Chyba při spouštění naslouchání:', error);
        }
    }

    /**
     * Zastavení naslouchání
     */
    stopListening() {
        if (!this.recognition) return;

        this.recognition.stop();
        this.isListening = false;
        this.updateUI();
        console.log('Voice Bot: Naslouchání zastaveno');
    }

    /**
     * Povolení voice bota
     */
    enable() {
        this.isEnabled = true;
        this.startListening();
        this.speak('Hlasové ovládání bylo zapnuto');
        console.log('Voice Bot: Povolen');
    }

    /**
     * Zakázání voice bota
     */
    disable() {
        this.isEnabled = false;
        this.stopListening();
        this.speak('Hlasové ovládání bylo vypnuto');
        console.log('Voice Bot: Zakázán');
    }

    /**
     * Přepnutí stavu voice bota
     */
    toggle() {
        if (this.isEnabled) {
            this.disable();
        } else {
            this.enable();
        }
    }

    /**
     * Zobrazení zprávy o nepodporovaném prohlížeči
     */
    showUnsupportedMessage() {
        if (typeof addMessage === 'function') {
            addMessage('Hlasové ovládání není podporováno ve vašem prohlížeči. Použijte Chrome, Edge nebo Firefox.', false);
        }
    }

    /**
     * Zobrazení průběžného výsledku rozpoznávání
     */
    showInterimResult(text) {
        if (window.VoiceBotUI) {
            window.VoiceBotUI.showInterimResult(text);
            return;
        }

        // Fallback
        const indicator = document.getElementById('voiceBotIndicator');
        if (indicator) {
            const statusText = indicator.querySelector('.voice-status-text');
            if (statusText) {
                statusText.textContent = `Naslouchám: "${text}"`;
            }
        }
    }

    /**
     * Zobrazení rozpoznaného příkazu
     */
    showRecognizedCommand(command) {
        if (window.VoiceBotUI) {
            window.VoiceBotUI.showRecognizedCommand(command);
            return;
        }

        // Fallback
        const indicator = document.getElementById('voiceBotIndicator');
        if (indicator) {
            const statusText = indicator.querySelector('.voice-status-text');
            if (statusText) {
                statusText.textContent = `Rozpoznáno: "${command}"`;

                // Resetování textu po 3 sekundách
                setTimeout(() => {
                    if (this.isListening) {
                        statusText.textContent = 'Naslouchám...';
                    } else {
                        statusText.textContent = 'Neaktivní';
                    }
                }, 3000);
            }
        }
    }

    /**
     * Zpracování chyby rozpoznávání řeči
     */
    handleSpeechError(error) {
        let errorMessage = 'Chyba rozpoznávání řeči';

        switch (error) {
            case 'no-speech':
                errorMessage = 'Nebyla detekována žádná řeč';
                break;
            case 'audio-capture':
                errorMessage = 'Chyba při zachytávání zvuku';
                break;
            case 'not-allowed':
                errorMessage = 'Přístup k mikrofonu byl zamítnut';
                break;
            case 'network':
                errorMessage = 'Chyba sítě při rozpoznávání řeči';
                break;
            default:
                errorMessage = `Neznámá chyba: ${error}`;
        }

        console.error('Voice Bot:', errorMessage);

        // Zobrazení chyby v UI
        if (typeof addMessage === 'function') {
            addMessage(`Hlasové ovládání: ${errorMessage}`, false);
        }
    }

    /**
     * Aktualizace uživatelského rozhraní
     */
    updateUI() {
        // Aktualizace přes VoiceBotUI pokud je dostupné
        if (window.VoiceBotUI) {
            window.VoiceBotUI.updateStatus(this.getStatus());
            return;
        }

        // Fallback na přímou aktualizaci DOM elementů
        const toggleButton = document.getElementById('voiceBotToggle');
        const indicator = document.getElementById('voiceBotIndicator');

        if (toggleButton) {
            toggleButton.classList.toggle('active', this.isEnabled);
            toggleButton.title = this.isEnabled ? 'Vypnout hlasové ovládání' : 'Zapnout hlasové ovládání';
        }

        if (indicator) {
            indicator.classList.toggle('listening', this.isListening);
            indicator.classList.toggle('enabled', this.isEnabled);

            const statusText = indicator.querySelector('.voice-status-text');
            if (statusText) {
                if (this.isListening) {
                    statusText.textContent = 'Naslouchám...';
                } else if (this.isEnabled) {
                    statusText.textContent = 'Připraven';
                } else {
                    statusText.textContent = 'Neaktivní';
                }
            }
        }
    }

    /**
     * Získání stavu voice bota
     */
    getStatus() {
        return {
            isEnabled: this.isEnabled,
            isListening: this.isListening,
            language: this.currentLanguage,
            commandsCount: this.voiceCommands.size
        };
    }

    /**
     * Nastavení jazyka
     */
    setLanguage(language) {
        this.currentLanguage = language;
        if (this.recognition) {
            this.recognition.lang = language;
        }
        console.log('Voice Bot: Jazyk nastaven na', language);
    }

    /**
     * Přidání vlastního hlasového příkazu
     */
    addVoiceCommand(command, action) {
        this.voiceCommands.set(command.toLowerCase(), action);
        console.log('Voice Bot: Přidán hlasový příkaz:', command);
    }

    /**
     * Odstranění hlasového příkazu
     */
    removeVoiceCommand(command) {
        this.voiceCommands.delete(command.toLowerCase());
        console.log('Voice Bot: Odstraněn hlasový příkaz:', command);
    }

    /**
     * Získání seznamu dostupných příkazů
     */
    getAvailableCommands() {
        return Array.from(this.voiceCommands.keys());
    }
}

// Globální instance voice bota
let voiceBot = null;

// Inicializace voice bota po načtení stránky
document.addEventListener('DOMContentLoaded', function() {
    // Čekáme na načtení ostatních modulů
    setTimeout(() => {
        voiceBot = new VoiceBot();
        console.log('Voice Bot: Globální instance vytvořena');

        // Přidání do globálního objektu window pro snadný přístup
        window.VoiceBot = voiceBot;
    }, 1000);
});

// Export pro použití v jiných modulech
if (typeof module !== 'undefined' && module.exports) {
    module.exports = VoiceBot;
}
