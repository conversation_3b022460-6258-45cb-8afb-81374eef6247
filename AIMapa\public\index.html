<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Map - Into the known v0.3.8.5</title>
    <link rel="icon" href="app/icons8-favicon.gif" type="image/gif">
    <link rel="stylesheet" href="app/styles.css">
    <!-- Leaflet CSS pro mapu -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css">
    <!-- Leaflet Routing Machine CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet-routing-machine@3.2.12/dist/leaflet-routing-machine.css">
    <!-- OSM Buildings CSS -->
    <link rel="stylesheet" href="https://cdn.osmbuildings.org/4.1.1/OSMBuildings.css">
    <!-- Three.js pro 3D glóbus -->
    <script src="https://cdn.jsdelivr.net/npm/three@0.160.0/build/three.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.160.0/examples/js/controls/OrbitControls.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.160.0/examples/js/loaders/GLTFLoader.js"></script>
    <!-- Globe.gl pro 3D glóbus -->
    <script src="https://unpkg.com/globe.gl"></script>
    <!-- Cesium CSS a JS (ponecháno pro zpětnou kompatibilitu) -->
    <link href="https://cesium.com/downloads/cesiumjs/releases/1.111/Build/Cesium/Widgets/widgets.css" rel="stylesheet">
    <script src="https://cesium.com/downloads/cesiumjs/releases/1.111/Build/Cesium/Cesium.js"></script>

    <!-- CSS pro novinky a další funkce -->
    <link rel="stylesheet" href="app/updates-notification.css">
    <link rel="stylesheet" href="app/user-progress.css">
    <link rel="stylesheet" href="app/route-utils.css">
    <link rel="stylesheet" href="app/feedback-survey.css">
    <link rel="stylesheet" href="app/virtual-work.css">
    <link rel="stylesheet" href="app/work-history-icons.css">
    <link rel="stylesheet" href="app/task-definition.css">
    <link rel="stylesheet" href="app/money-indicator-enhanced.css">
    <link rel="stylesheet" href="app/housing-services.css">
    <link rel="stylesheet" href="app/achievements.css">
    <link rel="stylesheet" href="app/idle-detection.css">
    <link rel="stylesheet" href="app/crypto-finances.css">
    <link rel="stylesheet" href="app/user-accounts.css">
    <link rel="stylesheet" href="app/simple-work-dialog.css">
    <!-- Supabase integrace -->
    <link rel="stylesheet" href="app/supabase-auth.css">
    <!-- Přihlašovací obrazovka -->
    <link rel="stylesheet" href="app/auth-screen.css">
    <!-- Bezpečnostní utility -->
    <link rel="stylesheet" href="app/security-utils.css">
    <!-- Voice Bot -->
    <link rel="stylesheet" href="app/voice-bot.css">
    <!-- Supabase klient -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
</head>
<body>
    <!-- Ikona pro zobrazení novinek a aktualizací byla odstraněna - novinky jsou nyní dostupné přes menu příkazů -->

    <div class="container">
        <header>
            <h1>AI Map - Into the known</h1>
            <button id="settingsButton" class="settings-button" aria-label="Nastavení">
                <i class="icon">⚙️</i>
            </button>
        </header>

        <main>
            <div class="map-container">
                <div class="status-bar">
                    <span class="status-icon">✓</span>
                    Všechno je funkční
                </div>

                <h2>Mapa aktivit</h2>
                <div class="map-wrapper">
                    <div id="map"></div>
                    <div id="cesiumContainer" class="cesium-container"></div>
                    <div id="threeGlobeContainer" class="three-globe-container"></div>
                    <button id="fullscreenButton" class="map-control-btn" title="Fullscreen režim">
                        <i class="icon">⛶</i>
                    </button>
                    <button id="toggleGlobeMode" class="map-control-btn" title="Glóbus režim">
                        <i class="icon">🌎</i>
                    </button>
                    <button id="exitGlobeMode" class="map-control-btn" title="Zpět na mapu" style="display: none;">
                        <i class="icon">🗺️</i>
                    </button>
                    <div id="coordinates" class="coordinates-display"></div>
                </div>
                <div class="fullscreen-overlay"></div>

                <div class="route-info" id="routeInfo">
                    <div class="info-item">
                        <span class="info-label">Vzdálenost:</span>
                        <span class="info-value" id="routeDistance">-</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Čas cesty:</span>
                        <span class="info-value" id="routeTime">-</span>
                    </div>
                </div>

                <div class="controls">
                    <button class="btn" id="addActivity">
                        <i class="icon">📍</i> Přidat aktivitu
                    </button>
                    <button class="btn" id="calculateRoute">
                        <i class="icon">🗺️</i> Vypočítat trasu
                    </button>
                    <button class="btn" id="clearMap">
                        <i class="icon">🗑️</i> Vymazat mapu
                    </button>
                    <button class="btn" id="printMap">
                        <i class="icon">🖨️</i> Vytisknout mapu
                    </button>
                </div>
            </div>

            <div class="ai-assistant" id="aiAssistant">
                <div class="chat-header">
                    <div class="chat-drag-handle">⋮⋮</div>
                    <h2>AI Asistent</h2>
                    <div class="chat-controls">
                        <button id="minimizeMainChat" class="chat-control-btn minimize-btn" title="Minimalizovat chat">−</button>
                    </div>
                </div>
                <div class="chat-content">
                    <div class="chat-messages" id="chatMessages">
                        <div class="message ai">
                            Trasa bude vypočítána po silnicích. Toto je simulace - v reálné aplikaci by byla použita přesná silniční data.
                        </div>
                    </div>
                    <div class="chat-input">
                        <input type="text" id="messageInput" placeholder="Napište zprávu...">
                        <button class="send-btn" id="sendMessage">➤</button>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Settings Modal -->
    <div id="settingsModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Nastavení aplikace</h2>
                <span class="close-button">&times;</span>
            </div>
            <div class="modal-body">
                <div class="settings-section">
                    <h3>Barevné schéma</h3>
                    <div class="color-options">
                        <button class="color-option blue active" data-color="blue"></button>
                        <button class="color-option purple" data-color="purple"></button>
                        <button class="color-option green" data-color="green"></button>
                        <button class="color-option orange" data-color="orange"></button>
                    </div>
                </div>

                <div class="settings-section">
                    <h3>Tmavý režim</h3>
                    <div class="toggle-container">
                        <span>Vypnuto</span>
                        <label class="switch">
                            <input type="checkbox" id="darkModeToggle" checked>
                            <span class="slider round"></span>
                        </label>
                        <span>Zapnuto</span>
                    </div>
                </div>

                <div class="settings-section">
                    <h3>Menu příkazů</h3>
                    <div class="toggle-container">
                        <span>Vypnuto</span>
                        <label class="switch">
                            <input type="checkbox" id="commandsMenuToggle" checked>
                            <span class="slider round"></span>
                        </label>
                        <span>Zapnuto</span>
                    </div>
                </div>


                <div class="settings-section">
                    <h3>Design bodů na mapě</h3>
                    <div class="marker-styles-container">
                        <div class="marker-style-options">
                            <div class="marker-style-option" data-marker-style="circle">
                                <div class="marker-preview circle-marker">
                                    <span>1</span>
                                </div>
                                <div class="marker-style-label">Kruh</div>
                            </div>
                            <div class="marker-style-option" data-marker-style="square">
                                <div class="marker-preview square-marker">
                                    <span>2</span>
                                </div>
                                <div class="marker-style-label">Čtverec</div>
                            </div>
                            <div class="marker-style-option" data-marker-style="diamond">
                                <div class="marker-preview diamond-marker">
                                    <span>3</span>
                                </div>
                                <div class="marker-style-label">Diamant</div>
                            </div>
                            <div class="marker-style-option" data-marker-style="pin">
                                <div class="marker-preview pin-marker">
                                    <span>4</span>
                                </div>
                                <div class="marker-style-label">Pin</div>
                            </div>
                            <div class="marker-style-option" data-marker-style="star">
                                <div class="marker-preview star-marker">
                                    <span>5</span>
                                </div>
                                <div class="marker-style-label">Hvězda</div>
                            </div>
                        </div>
                    </div>
                    <div class="marker-effects-toggle">
                        <div class="checkbox-container">
                            <input type="checkbox" id="markerEffectsToggle" checked>
                            <label for="markerEffectsToggle">Animace a efekty bodů</label>
                        </div>
                    </div>
                </div>

                <div class="settings-section">
                    <h3>AI API Nastavení</h3>
                    <div class="api-selection">
                        <label>Vyberte AI API:</label>
                        <div class="api-options">
                            <div class="api-option">
                                <input type="radio" id="api1" name="apiOption" checked>
                                <label for="api1">OpenAI GPT-4</label>
                            </div>
                            <div class="api-option">
                                <input type="radio" id="api2" name="apiOption">
                                <label for="api2">Google Gemini</label>
                            </div>
                            <div class="api-option">
                                <input type="radio" id="api3" name="apiOption">
                                <label for="api3">Anthropic Claude</label>
                            </div>
                        </div>
                    </div>

                    <div class="api-key-section">
                        <label for="apiKey">API klíč:</label>
                        <div class="api-key-input">
                            <input type="password" id="apiKey" placeholder="Zadejte API klíč">
                            <button id="showApiKey" class="show-key-btn">Zobrazit</button>
                        </div>
                        <div class="checkbox-container">
                            <input type="checkbox" id="saveApiKey">
                            <label for="saveApiKey">Uložit API klíč</label>
                        </div>
                    </div>
                </div>

                <div class="settings-actions">
                    <button id="saveSettings" class="btn primary-btn">Uložit</button>
                    <button id="cancelSettings" class="btn secondary-btn">Zrušit</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Dancer Reservation Modal -->
    <div id="dancerReservationModal" class="modal">
        <div class="modal-content dancer-modal-content">
            <div class="modal-header">
                <h2>Rezervace tanečnice</h2>
                <span class="close-button dancer-close-button">&times;</span>
            </div>
            <div class="modal-body">
                <div class="dancer-selection-section">
                    <h3>Vyberte tanečnici</h3>
                    <div class="dancers-grid">
                        <div class="dancer-card" data-dancer="Nikol">
                            <div class="dancer-image-container">
                                <div class="dancer-image dancer-image-1"></div>
                            </div>
                            <div class="dancer-info">
                                <h4>Nikol</h4>
                                <p>Věk: 23 | Výška: 175 cm</p>
                                <div class="dancer-rating">★★★★★</div>
                            </div>
                        </div>
                        <div class="dancer-card" data-dancer="Monika">
                            <div class="dancer-image-container">
                                <div class="dancer-image dancer-image-2"></div>
                            </div>
                            <div class="dancer-info">
                                <h4>Monika</h4>
                                <p>Věk: 25 | Výška: 168 cm</p>
                                <div class="dancer-rating">★★★★★</div>
                            </div>
                        </div>
                        <div class="dancer-card" data-dancer="Tereza">
                            <div class="dancer-image-container">
                                <div class="dancer-image dancer-image-3"></div>
                            </div>
                            <div class="dancer-info">
                                <h4>Tereza</h4>
                                <p>Věk: 24 | Výška: 172 cm</p>
                                <div class="dancer-rating">★★★★★</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="reservation-details-section">
                    <h3>Detaily rezervace</h3>
                    <div class="form-group">
                        <label for="dancerReservationDate">Datum:</label>
                        <input type="date" id="dancerReservationDate" class="popup-input" value="">
                    </div>
                    <div class="form-group">
                        <label for="dancerReservationTime">Hodina:</label>
                        <input type="time" id="dancerReservationTime" class="popup-input" value="22:00">
                    </div>
                    <div class="form-group">
                        <label for="dancerReservationHours">Počet hodin:</label>
                        <select id="dancerReservationHours" class="popup-input">
                            <option value="1">1 hodina (5000 Kč)</option>
                            <option value="2" selected>2 hodiny (9000 Kč)</option>
                            <option value="3">3 hodiny (12000 Kč)</option>
                            <option value="4">4 hodiny (15000 Kč)</option>
                            <option value="night">Celá noc (25000 Kč)</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="dancerReservationName">Vaše jméno:</label>
                        <input type="text" id="dancerReservationName" class="popup-input" placeholder="Zadejte vaše jméno">
                    </div>
                    <div class="form-group">
                        <label for="dancerReservationContact">Kontakt:</label>
                        <input type="text" id="dancerReservationContact" class="popup-input" placeholder="Telefon nebo email">
                    </div>
                    <div class="form-group">
                        <label for="dancerReservationNote">Speciální požadavky:</label>
                        <textarea id="dancerReservationNote" class="popup-input" placeholder="Např. preference hudby, speciální přání..."></textarea>
                    </div>
                </div>

                <div class="reservation-terms">
                    <p><strong>Poznámka:</strong> Rezervace je závazná a podléhá pravidlům klubu. Platba probíhá na místě. Storno rezervace je možné nejpozději 24 hodin před rezervovaným termínem.</p>
                </div>

                <div class="reservation-actions">
                    <button id="confirmDancerReservation" class="btn primary-btn">Potvrdit rezervaci</button>
                    <button id="cancelDancerReservation" class="btn secondary-btn">Zrušit</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Leaflet JS pro mapu - přidáno defer=false a async=false pro zajištění správného pořadí načítání -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js" defer="false" async="false"></script>
    <!-- OpenRouteService API pro přesné směrování po silnicích -->
    <script src="https://unpkg.com/leaflet-routing-machine@3.2.12/dist/leaflet-routing-machine.js" defer="false" async="false"></script>
    <!-- OSM Buildings JS pro 3D budovy -->
    <script src="https://cdn.osmbuildings.org/classic/0.2.2b/OSMBuildings-Leaflet.js" defer="false" async="false"></script>
    <!-- Cesium JS je již načten v hlavičce -->
    <!-- Přímá implementace Globe.gl -->
    <script src="app/globe-simple.js"></script>
    <!-- Hlavní skript aplikace - přidáno defer pro zajištění, že se načte až po Leaflet.js -->
    <script src="app/script.js" defer></script>
    <!-- Skripty pro novinky a menu příkazů -->
    <script src="app/updates-notification.js"></script>
    <script>
        // Přímá inicializace UpdatesNotification
        document.addEventListener('DOMContentLoaded', function() {
            if (typeof UpdatesNotification !== 'undefined') {
                console.log('Přímá inicializace UpdatesNotification...');
                UpdatesNotification.init();
            } else {
                console.error('UpdatesNotification modul nebyl nalezen!');
            }
        });
    </script>

    <!-- Moduly aplikace -->
    <script src="app/user-progress.js"></script>
    <script src="app/user-progress-extensions.js"></script>
    <script src="app/transport-connections.js"></script>
    <script src="app/feedback-survey.js"></script>
    <script src="app/food-services.js"></script>
    <script src="app/medical-services.js"></script>
    <script src="app/transport-services.js"></script>
    <script src="app/dark-sky-effects.js"></script>
    <script src="app/draggable-elements.js"></script>
    <script src="app/task-system.js"></script>
    <script src="app/car-sales.js"></script>
    <script src="app/virtual-work.js"></script>
    <script src="app/reward-system.js"></script>
    <script src="app/commands-menu.js"></script>
    <script src="app/fix-menu.js"></script>
    <script src="app/business-markers.js"></script>
    <script src="app/business-data-loader.js"></script>
    <script src="app/money-indicator-enhanced.js"></script>
    <script src="app/triple-click-handler.js"></script>
    <script src="app/bug-reporter.js"></script>
    <script src="app/housing-services.js"></script>
    <script src="app/achievements.js"></script>
    <script src="app/idle-detection.js"></script>
    <script src="app/crypto-finances.js"></script>
    <script src="app/user-accounts.js"></script>
    <script src="app/simple-work-dialog.js"></script>
    <!-- Supabase a Netlify integrace -->
    <script src="app/supabase-client.js"></script>
    <script src="app/supabase-auth.js"></script>
    <script src="app/netlify-integration.js"></script>
    <script src="app/security-utils.js"></script>
    <script src="app/local-auth.js"></script>
    <script src="app/hybrid-auth.js"></script>
    <script src="app/auth-screen.js"></script>
    <!-- Voice Bot -->
    <script src="app/voice-bot.js"></script>
    <script src="app/voice-bot-ui.js"></script>
    <!-- Debugovací skript pro mapu -->
    <script src="app/map-debug.js"></script>
</body>
</html>
